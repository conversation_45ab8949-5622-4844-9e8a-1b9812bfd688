# Gemini Project Context

This document provides context for the Gemini AI assistant to understand and effectively assist with this project.

## Project Overview

This is a monorepo project for a web application. It uses Next.js for the frontend and has a separate package for UI components. The project is managed with pnpm workspaces and Turbo for monorepo management.

## Tech Stack

- TypeScript
- Next.js
- React
- Tailwind CSS
- shadcn/ui
- pnpm
- Turbo

## Project Structure

The project is a monorepo with the following structure:

- `apps/web`: The Next.js web application.
- `packages/ui`: A shared UI component library.
- `packages/eslint-config`: Shared ESLint configuration.
- `packages/typescript-config`: Shared TypeScript configuration.

## Available Scripts

The following scripts are available in the root `package.json` and can be run with `pnpm <script>`:

- `build`: Build all the packages in the monorepo.
- `dev`: Run all the packages in development mode.
- `lint`: Lint all the packages in the monorepo.
- `format`: Format all the files in the monorepo.
- `check:deprecated`: Check for deprecated packages.

## How to run the project

To run the project in development mode, use the following command:

```bash
pnpm dev
```
