// app/login/page.tsx

import SetPassword from "@workspace/ui/components/SetPassword";
import ThemeToggleButton from "@workspace/ui/components/theme-toggle-button";
import Image from "next/image";
import PillTabs from "@workspace/ui/components/pill-tabs";

export default function LoginPage() {
  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="absolute top-4 right-4">
        <ThemeToggleButton start="top-right" />
      </div>

      <div className="w-full max-w-7xl bg-white dark:bg-gray-800 rounded-2xl shadow-xl border overflow-hidden grid grid-cols-1 md:grid-cols-[2fr_3fr] min-h-[50rem]">
        {/* Left - Image */}
        <div className="relative">
          <Image
            fill
            src="/images/signin_img.jpg"
            alt="Doctor smiling"
            className="object-cover"
            priority
          />
        </div>

        {/* Right - Form */}
        <div className="flex justify-center items-center p-8">
          <div className="space-y-8 w-full max-w-sm">
            <div className="text-center space-y-4">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Welcome Back
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Welcome Back, Please enter Your details
              </p>

              <PillTabs
                defaultActiveId="signin"
                className="mx-auto w-fit"
                tabs={[
                  { id: "signin", label: "Sign In" },
                  { id: "signup", label: "Sign Up" },
                ]}
              />
            </div>

            <form className="space-y-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                      />
                    </svg>
                  </div>
                  <input
                    type="email"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                    defaultValue="<EMAIL>"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <svg
                      className="h-5 w-5 text-green-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                </div>
              </div>

              <div>
                <SetPassword label="Input with password strength indicator" />
              </div>

              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200"
              >
                Continue
              </button>
            </form>

            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Don't have an account?{" "}
                <button className="text-blue-600 hover:text-blue-700 font-medium">
                  Request Now
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Copyright Footer */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Copyright @wework 2022 | Privacy Policy
        </p>
      </div>
    </div>
  );
}
